// Copyright (c) 1998-2017 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION CNT.5.0.1
// ============================================================================
// CHANGE LOG
// PEPL.8.2.0P.R2.7.8 : 2018-06-22, harry.tang, PEPL-1071
// ============================================================================

package com.core.pepl.item.action.actionContext;

import com.core.cbx.action.BaseActionContext;
import com.core.cbx.data.entity.DynamicEntity;

/**
 * <AUTHOR>
 *
 */
public class ItemCustom02 extends BaseActionContext {
    public ItemCustom02(final DynamicEntity entity) {
        super(entity);
    }
}
