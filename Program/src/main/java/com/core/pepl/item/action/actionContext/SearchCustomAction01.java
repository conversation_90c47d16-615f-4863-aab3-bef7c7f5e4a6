// Copyright (c) 1998-2017 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION CNT.5.0.1
// ============================================================================
// CHANGE LOG
// CNT.5.0.1 : 2017-07-06, caroline.qu, PEPL-26,creation
// ============================================================================

package com.core.pepl.item.action.actionContext;

import java.util.List;

import com.core.cbx.action.BatchActionContext;
import com.core.cbx.data.entity.DynamicEntity;

/**
 * <AUTHOR>
 *
 */
public class SearchCustomAction01 extends BatchActionContext{

    private static final long serialVersionUID = 1L;

    public SearchCustomAction01(final String viewId, final List<DynamicEntity> selectedList) {
        super(viewId, selectedList);
    }

}
