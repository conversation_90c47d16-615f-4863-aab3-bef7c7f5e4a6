// Copyright (c) 1998-2017 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION CNT.5.0.1
// ============================================================================
// CHANGE LOG
// CNT.5.0.1 : 2017-07-06, caroline.qu, PEPL-26,creation
// ============================================================================

package com.core.pepl.item.action.actionContext;

import com.core.cbx.action.BaseActionContext;
import com.core.cbx.data.entity.DynamicEntity;

/**
 * <AUTHOR>
 *
 */
public class ItemCustom01 extends BaseActionContext {
    public ItemCustom01(final DynamicEntity entity) {
        super(entity);
    }
}
