// Copyright (c) 1998-2018 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION CNT.5.0.1
// ============================================================================
// CHANGE LOG
// PEPL.8.2.0P.R2.15.0 : 2018-10-26, harry.tang, PEPL-1203
// ============================================================================
package com.core.pepl.item.action.actionContext;

import com.core.cbx.action.actionContext.CopyDoc;
import com.core.cbx.data.entity.DynamicEntity;

/**
 * <AUTHOR>
 *
 */
public class ItemCustom03 extends CopyDoc{
    public ItemCustom03(final DynamicEntity entity) {
        super(entity);
    }
}
