// Copyright (c) 1998-2021 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION PEPL.8.2.0P.2R.85.2
// ============================================================================
// CHANGE LOG
// PEPL.8.2.0P.2R.85.2 : 2021-05-10, don.dong, PEPL-1893
// ============================================================================
package com.core.pepl.item.action.actionContext;

import com.core.cbx.action.BaseActionContext;
import com.core.cbx.data.entity.DynamicEntity;

/**
 * <AUTHOR>
 *
 */
public class ItemCustom04 extends BaseActionContext {
    public ItemCustom04(final DynamicEntity entity) {
        super(entity);
    }
}
